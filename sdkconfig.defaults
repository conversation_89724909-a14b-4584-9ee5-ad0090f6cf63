# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF) 5.4.1 Project Minimal Configuration
#
CONFIG_IDF_TARGET="esp32c3"
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_COMPILER_OPTIMIZATION_PERF=y
CONFIG_FREERTOS_HZ=1000
CONFIG_BSP_LCD_DRAW_BUF_HEIGHT=160
CONFIG_LV_COLOR_16_SWAP=y
CONFIG_LV_MEM_CUSTOM=y
CONFIG_LV_MEMCPY_MEMSET_STD=y
CONFIG_LV_SPRINTF_CUSTOM=y
CONFIG_LV_TXT_BREAK_CHARS=" ,.;:-_)}"
CONFIG_LV_USE_SNAPSHOT=n
CONFIG_LV_BUILD_EXAMPLES=n
