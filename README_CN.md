# 音频循环录制与播放示例

本示例展示了使用 ESP-Hi 实现简单的音频录制和播放功能。用户可以通过按下按钮来录制音频，松开按钮后播放录制的音频。

## 功能特点

- 按住按钮进行音频录制
- 松开按钮停止录制并播放
- 最大录制时间：3秒
- 默认采样率：16kHz
- 音频格式：16位 PCM，单声道
- 实时录制和播放

## 硬件要求

- ESP-Hi 开发板

## 使用方法

1. 按住 BOOT 按钮开始录制
2. 在按住按钮的同时对着麦克风说话
3. 松开按钮停止录制并播放录制的音频
4. 录制的音频将通过内置扬声器播放

## 构建和运行

1. 设置 ESP-IDF 环境
   ```
2. 构建项目：
   ```bash
   idf.py build
   ```
3. 烧录项目：
   ```bash
   idf.py -p (PORT) flash monitor
   ```

## 故障排除

1. 如果没有声音：
   - 检查扬声器是否正确连接
   - 验证代码中的音量设置
   - 检查音频编解码器是否正确初始化

2. 如果录音质量较差：
   - 检查麦克风是否正确连接
   - 验证采样率和位深度设置
   - 检查麦克风附近是否有干扰源